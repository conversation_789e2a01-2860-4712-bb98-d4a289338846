# Helper Onsen CSV Combiner

This Go project combines two customer CSV files by the `Code` column, producing a merged CSV with all columns from both files.

## Project Structure

```
helper-onsen/
├── cmd/
│   └── main.go                # CLI entry point
├── internal/
│   └── combine/
│       └── combine.go         # CSV combining logic
├── onsen_customer_list_1.csv  # Example input file 1
├── onsen_customer_list_2.csv  # Example input file 2
├── go.mod                     # Go module definition
└── README.md                  # Project documentation
```

## Usage

### 1. Prepare Your CSV Files

- Place your two CSV files in the project directory (or provide their paths).
- The first CSV should have columns:
  - `#, Code, First Name, Last Name, Birthday, Customer Group, Telephone (Mobile), Email, Address`
- The second CSV should have columns:
  - `#, Code, NRIC, Membership No., First Visit, Preferred Employee, Gender, Nationality, Client Referral`

### 2. Build the Project

```
go build -o combine-csv ./cmd
```

### 3. Run the Program

```
./combine-csv <first.csv> <second.csv> <output.csv>
```

- `<first.csv>`: Path to the first CSV file
- `<second.csv>`: Path to the second CSV file
- `<output.csv>`: Path for the combined output CSV

**Example:**

```
./combine-csv onsen_customer_list_1.csv onsen_customer_list_2.csv combined.csv
```

### 4. Output

- The output CSV will contain all columns from both files, matched by the `Code` column.
- If a `Code` exists in only one file, the missing columns will be empty.
- The output will only have one `Code` column (no duplicates).

## Notes

- The combining logic is implemented in `internal/combine/combine.go`.
- You can modify or extend the logic as needed for other CSV formats.

---

If you need to combine different CSVs or change the matching logic, please update the code in `internal/combine/combine.go` or ask for further assistance.
