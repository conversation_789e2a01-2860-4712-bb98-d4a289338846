package combine

import (
	"encoding/csv"
	"fmt"
	"os"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"time"
)

// Helper functions for transformations
func standardizePhone(phone string) string {
	phone = strings.TrimSpace(phone)
	if phone == "" {
		return ""
	}
	// Remove all non-digits
	digits := ""
	for _, r := range phone {
		if r >= '0' && r <= '9' {
			digits += string(r)
		}
	}
	if strings.HasPrefix(digits, "65") {
		return "+" + digits
	}
	if len(digits) == 8 {
		return "+65" + digits
	}
	if len(digits) == 10 && strings.HasPrefix(digits, "65") {
		return "+" + digits
	}
	return phone // fallback
}

func standardizeEmail(email string) string {
	email = strings.TrimSpace(email)
	if email == "--" || email == "" {
		return "NULL"
	}
	return email
}

var nricRegex = regexp.MustCompile(`^[STFG]\d{7}[A-Z]$`)

func validateNRIC(nric string) string {
	nric = strings.ToUpper(strings.TrimSpace(nric))
	if nric == "" {
		return ""
	}
	if nricRegex.MatchString(nric) {
		return nric
	}
	return "INVALID"
}

func standardizeBirthday(birthday string) string {
	birthday = strings.TrimSpace(birthday)
	if birthday == "" {
		return ""
	}
	// Try common formats
	formats := []string{"02/01/2006", "2/1/2006", "2006-01-02", "01-02-2006", "1-2-2006"}
	for _, f := range formats {
		if t, err := time.Parse(f, birthday); err == nil {
			return t.Format("2006-01-02")
		}
	}
	return birthday // fallback
}

func extractExpiryFromGroup(group string) string {
	// Example: "VIP (exp: 2025-12-31)" => 2025-12-31
	group = strings.TrimSpace(group)
	re := regexp.MustCompile(`exp[:：]?\s*([0-9]{4}-[0-9]{2}-[0-9]{2})`)
	match := re.FindStringSubmatch(group)
	if len(match) > 1 {
		return match[1]
	}
	return ""
}

func defaultIfEmpty(val, def string) string {
	if strings.TrimSpace(val) == "" {
		return def
	}
	return val
}

// CombineCSVByCode combines two CSV files by the 'Code' column and writes the result to outputCSV.
func CombineCSVByCode(firstCSV, secondCSV, outputCSV string) error {
	// Open first CSV
	f1, err := os.Open(firstCSV)
	if err != nil {
		return fmt.Errorf("open first csv: %w", err)
	}
	defer f1.Close()
	reader1 := csv.NewReader(f1)
	rows1, err := reader1.ReadAll()
	if err != nil {
		return fmt.Errorf("read first csv: %w", err)
	}
	if len(rows1) < 1 {
		return fmt.Errorf("first csv has no rows")
	}
	// Open second CSV
	f2, err := os.Open(secondCSV)
	if err != nil {
		return fmt.Errorf("open second csv: %w", err)
	}
	defer f2.Close()
	reader2 := csv.NewReader(f2)
	rows2, err := reader2.ReadAll()
	if err != nil {
		return fmt.Errorf("read second csv: %w", err)
	}
	if len(rows2) < 1 {
		return fmt.Errorf("second csv has no rows")
	}

	// Find column indices
	findIdx := func(header []string, name string) int {
		for i, col := range header {
			if strings.EqualFold(strings.TrimSpace(col), name) {
				return i
			}
		}
		return -1
	}
	codeIdx1 := findIdx(rows1[0], "Code")
	codeIdx2 := findIdx(rows2[0], "Code")
	if codeIdx1 == -1 {
		return fmt.Errorf("'Code' column not found in first csv")
	}
	if codeIdx2 == -1 {
		return fmt.Errorf("'Code' column not found in second csv")
	}
	phoneIdx1 := findIdx(rows1[0], "Telephone (Mobile)")
	emailIdx1 := findIdx(rows1[0], "Email")
	birthdayIdx1 := findIdx(rows1[0], "Birthday")
	groupIdx1 := findIdx(rows1[0], "Customer Group")
	genderIdx2 := findIdx(rows2[0], "Gender")
	nationalityIdx2 := findIdx(rows2[0], "Nationality")
	nricIdx2 := findIdx(rows2[0], "NRIC")

	// Remove duplicate '#' column from second file if present
	hashIdx1 := findIdx(rows1[0], "#")
	hashIdx2 := findIdx(rows2[0], "#")
	outHeader := []string{}
	for i, col := range rows1[0] {
		if i != hashIdx1 {
			outHeader = append(outHeader, col)
		}
	}
	for i, col := range rows2[0] {
		if i != hashIdx2 && col != "Code" {
			outHeader = append(outHeader, col)
		}
	}
	// Add ExpiryDate column
	outHeader = append(outHeader, "ExpiryDate")

	// Build maps for fast lookup
	map1 := make(map[string][]string)
	for _, row := range rows1[1:] {
		if len(row) > codeIdx1 {
			map1[row[codeIdx1]] = row
		}
	}
	map2 := make(map[string][]string)
	for _, row := range rows2[1:] {
		if len(row) > codeIdx2 {
			map2[row[codeIdx2]] = row
		}
	}

	// Collect all unique codes
	uniqueCodes := make([]string, 0)
	codeSet := make(map[string]struct{})
	for code := range map1 {
		if _, exists := codeSet[code]; !exists {
			uniqueCodes = append(uniqueCodes, code)
			codeSet[code] = struct{}{}
		}
	}
	for code := range map2 {
		if _, exists := codeSet[code]; !exists {
			uniqueCodes = append(uniqueCodes, code)
			codeSet[code] = struct{}{}
		}
	}

	// For uniqueness validation
	phoneSet := make(map[string]string) // phone -> code
	emailSet := make(map[string]string) // email -> code
	codeSetUnique := make(map[string]struct{})
	var uniqueMu sync.Mutex

	// Write output
	outFile, err := os.Create(outputCSV)
	if err != nil {
		return fmt.Errorf("create output csv: %w", err)
	}
	defer outFile.Close()
	writer := csv.NewWriter(outFile)
	defer writer.Flush()

	if err := writer.Write(outHeader); err != nil {
		return fmt.Errorf("write header: %w", err)
	}

	nWorkers := runtime.NumCPU()
	jobs := make(chan string, nWorkers*2)
	results := make(chan []string, nWorkers*2)
	errChan := make(chan error, 1)
	var wg sync.WaitGroup

	for i := 0; i < nWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for code := range jobs {
				row1 := map1[code]
				row2 := map2[code]

				// Build output row, skipping duplicate # and Code columns
				outRow := []string{}
				// First file columns
				for i, val := range row1 {
					if i != hashIdx1 {
						outRow = append(outRow, val)
					}
				}
				// Second file columns (skip # and Code)
				for i, val := range row2 {
					if i != hashIdx2 && i != codeIdx2 {
						outRow = append(outRow, val)
					}
				}

				// Standardize phone
				if phoneIdx1 != -1 && len(row1) > phoneIdx1 {
					outRow[phoneIdx1-(func() int { if hashIdx1 != -1 && phoneIdx1 > hashIdx1 { return 1 }; return 0 }())] = standardizePhone(row1[phoneIdx1])
				}
				// Standardize email
				if emailIdx1 != -1 && len(row1) > emailIdx1 {
					outRow[emailIdx1-(func() int { if hashIdx1 != -1 && emailIdx1 > hashIdx1 { return 1 }; return 0 }())] = standardizeEmail(row1[emailIdx1])
				}
				// Standardize birthday
				if birthdayIdx1 != -1 && len(row1) > birthdayIdx1 {
					outRow[birthdayIdx1-(func() int { if hashIdx1 != -1 && birthdayIdx1 > hashIdx1 { return 1 }; return 0 }())] = standardizeBirthday(row1[birthdayIdx1])
				}
				// Validate NRIC
				if nricIdx2 != -1 && len(row2) > nricIdx2 {
					outRow[len(outRow)-(len(row2)-nricIdx2)] = validateNRIC(row2[nricIdx2])
				}
				// Default Gender/Nationality
				if genderIdx2 != -1 && len(row2) > genderIdx2 {
					idx := len(outRow)-(len(row2)-genderIdx2)
					outRow[idx] = defaultIfEmpty(row2[genderIdx2], "Unknown")
				}
				if nationalityIdx2 != -1 && len(row2) > nationalityIdx2 {
					idx := len(outRow)-(len(row2)-nationalityIdx2)
					outRow[idx] = defaultIfEmpty(row2[nationalityIdx2], "Singaporean")
				}
				// Extract expiry date
				expiry := ""
				if groupIdx1 != -1 && len(row1) > groupIdx1 {
					expiry = extractExpiryFromGroup(row1[groupIdx1])
				}
				outRow = append(outRow, expiry)

				// Validate uniqueness (Code, Phone, Email)
				codeVal := code
				phoneVal := ""
				if phoneIdx1 != -1 && len(row1) > phoneIdx1 {
					phoneVal = outRow[phoneIdx1-(func() int { if hashIdx1 != -1 && phoneIdx1 > hashIdx1 { return 1 }; return 0 }())]
				}
				emailVal := ""
				if emailIdx1 != -1 && len(row1) > emailIdx1 {
					emailVal = outRow[emailIdx1-(func() int { if hashIdx1 != -1 && emailIdx1 > hashIdx1 { return 1 }; return 0 }())]
				}
				uniqueMu.Lock()
				if codeVal != "" {
					if _, exists := codeSetUnique[codeVal]; exists {
						uniqueMu.Unlock()
						errChan <- fmt.Errorf("duplicate Code: %s", codeVal)
						return
					}
					codeSetUnique[codeVal] = struct{}{}
				}
				if phoneVal != "" && phoneVal != "NULL" {
					if prev, exists := phoneSet[phoneVal]; exists {
						uniqueMu.Unlock()
						errChan <- fmt.Errorf("duplicate Phone: %s (Codes: %s, %s)", phoneVal, prev, codeVal)
						return
					}
					phoneSet[phoneVal] = codeVal
				}
				if emailVal != "" && emailVal != "NULL" {
					if prev, exists := emailSet[emailVal]; exists {
						uniqueMu.Unlock()
						errChan <- fmt.Errorf("duplicate Email: %s (Codes: %s, %s)", emailVal, prev, codeVal)
						return
					}
					emailSet[emailVal] = codeVal
				}
				uniqueMu.Unlock()

				results <- outRow
			}
		}()
	}

	// Feed jobs
	go func() {
		for _, code := range uniqueCodes {
			jobs <- code
		}
		close(jobs)
	}()

	// Writer goroutine
	writeErr := make(chan error, 1)
	go func() {
		for i := 0; i < len(uniqueCodes); i++ {
			row := <-results
			if err := writer.Write(row); err != nil {
				writeErr <- fmt.Errorf("write row: %w", err)
				return
			}
		}
		writeErr <- nil
	}()

	wg.Wait()
	close(results)

	select {
	case err := <-errChan:
		return err
	case err := <-writeErr:
		if err != nil {
			return err
		}
	}

	return nil
}

