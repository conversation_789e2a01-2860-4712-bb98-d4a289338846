package main

import (
	"fmt"
	"log"
	"os"

	"helper-onsen/internal/combine"
)

func main() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: helper-onsen <first.csv> <second.csv> <output.csv>")
		os.Exit(1)
	}
	firstCSV := os.Args[1]
	secondCSV := os.Args[2]
	outputCSV := os.Args[3]

	err := combine.CombineCSVByCode(firstCSV, secondCSV, outputCSV)
	if err != nil {
		log.Fatalf("Error combining CSVs: %v", err)
	}
	fmt.Println("Combined CSV written to", outputCSV)
}
